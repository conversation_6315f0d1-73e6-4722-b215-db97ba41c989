import React, { useState, useEffect } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { questionService } from '@/services/questionService';
import { chapterService, Chapter } from '@/services/chapterService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router, useLocalSearchParams } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function QuestionAddScreen() {
  const { chapter_id, course_id, courseName } = useLocalSearchParams<{ chapter_id: string, course_id: string, courseName: string }>();
  const [questionText, setQuestionText] = useState('');
  const [explanation, setExplanation] = useState('');
  const [difficultyLevel, setDifficultyLevel] = useState('');
  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (chapter_id) {
      const fetchChapter = async () => {
        try {
          const chapterData = await chapterService.getChapter(chapter_id);
          setChapter(chapterData);
        } catch (error) {
          console.error('Failed to fetch chapter:', error);
        }
      };
      fetchChapter();
    }
  }, [chapter_id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!questionText.trim()) {
      newErrors.questionText = 'متن سوال الزامی است';
    }

    if (!explanation.trim()) {
      newErrors.explanation = 'توضیحات الزامی است';
    }

    if (!difficultyLevel.trim()) {
      newErrors.difficultyLevel = 'سطح دشواری الزامی است';
    } else if (isNaN(Number(difficultyLevel)) || Number(difficultyLevel) < 1 || Number(difficultyLevel) > 5) {
      newErrors.difficultyLevel = 'سطح دشواری باید عددی بین 1 تا 5 باشد';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddQuestion = async () => {
    if (!validateForm()) {
      return;
    }

    if (!chapter_id) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه فصل یافت نشد.');
      setShowAlert(true);
      return;
    }

    setLoading(true);
    try {
      await questionService.createQuestion({
        question_text: questionText,
        explanation,
        difficulty_level: difficultyLevel,
        chapter_id: chapter_id,
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('سوال با موفقیت اضافه شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('افزودن سوال انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add question:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addQuestion') || 'افزودن سوال'}
        onSavePress={() => {}} // Handled by form submit button
        saveDisabled={loading}
        showSave={false} // Using form submit button instead
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('questionDetails') || 'جزئیات سوال'}>
            {courseName && (
              <PaperBody style={styles.chapterInfo}>
                دوره: {courseName} (شناسه: {course_id})
              </PaperBody>
            )}
            {chapter && (
              <PaperBody style={styles.chapterInfo}>
                فصل: {chapter.name} (شناسه: {chapter_id})
              </PaperBody>
            )}

            <PaperFormField
              label={t('questionText') || 'متن سوال'}
              error={errors.questionText}
              required
            >
              <PaperTextInput
                value={questionText}
                onChangeText={setQuestionText}
                placeholder="متن سوال را وارد کنید"
                disabled={loading}
                multiline
                numberOfLines={4}
                error={!!errors.questionText}
              />
            </PaperFormField>

            <PaperFormField
              label={t('explanation') || 'توضیحات'}
              error={errors.explanation}
              required
            >
              <PaperTextInput
                value={explanation}
                onChangeText={setExplanation}
                placeholder="توضیحات سوال را وارد کنید"
                disabled={loading}
                multiline
                numberOfLines={3}
                error={!!errors.explanation}
              />
            </PaperFormField>

            <PaperFormField
              label={t('difficultyLevel') || 'سطح دشواری (1-5)'}
              error={errors.difficultyLevel}
              required
            >
              <PaperTextInput
                value={difficultyLevel}
                onChangeText={setDifficultyLevel}
                placeholder="عدد بین 1 تا 5"
                disabled={loading}
                keyboardType="numeric"
                error={!!errors.difficultyLevel}
              />
            </PaperFormField>

            <PaperButton
              title={loading ? 'در حال افزودن...' : t('addQuestion') || 'افزودن سوال'}
              onPress={handleAddQuestion}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  chapterInfo: {
    marginBottom: 16,
    textAlign: 'center',
    color: '#666',
  },
  submitButton: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
