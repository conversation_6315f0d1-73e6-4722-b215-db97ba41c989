import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView, View } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { Question, questionService } from '@/services/questionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function QuestionEditScreen() {
  const { id } = useLocalSearchParams();
  const [questionText, setQuestionText] = useState('');
  const [explanation, setExplanation] = useState('');
  const [difficultyLevel, setDifficultyLevel] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (id) {
      const fetchQuestion = async () => {
        try {
          const data = await questionService.getQuestion(id as string);
          setQuestionText(data.question_text);
          setExplanation(data.explanation);
          setDifficultyLevel(data.difficulty_level.toString());
          setChapterId(data.chapter_id);
        } catch (error) {
          setAlertTitle(t('error') || 'خطا');
          setAlertMessage('خطا در دریافت جزئیات سوال برای ویرایش.');
          setShowAlert(true);
          console.error('Failed to fetch question:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchQuestion();
    } else {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه سوال برای ویرایش ارائه نشده است.');
      setShowAlert(true);
      setLoading(false);
    }
  }, [id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!questionText.trim()) {
      newErrors.questionText = 'متن سوال الزامی است';
    }

    if (!explanation.trim()) {
      newErrors.explanation = 'توضیحات الزامی است';
    }

    if (!difficultyLevel.trim()) {
      newErrors.difficultyLevel = 'سطح دشواری الزامی است';
    } else if (isNaN(Number(difficultyLevel)) || Number(difficultyLevel) < 1 || Number(difficultyLevel) > 5) {
      newErrors.difficultyLevel = 'سطح دشواری باید عددی بین 1 تا 5 باشد';
    }

    if (!chapterId.trim()) {
      newErrors.chapterId = 'شناسه فصل الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateQuestion = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه سوال موجود نیست.');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await questionService.updateQuestion(id as string, {
        question_text: questionText,
        explanation,
        difficulty_level: Number(difficultyLevel),
        chapter_id: chapterId,
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('سوال با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('به‌روزرسانی سوال انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update question:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش سوال"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگذاری سوال برای ویرایش...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editQuestion') || 'ویرایش سوال'}
        onSavePress={handleUpdateQuestion}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('questionDetails') || 'جزئیات سوال'}>
            <PaperFormField
              label={t('questionText') || 'متن سوال'}
              error={errors.questionText}
              required
            >
              <PaperTextInput
                value={questionText}
                onChangeText={setQuestionText}
                placeholder="متن سوال را وارد کنید"
                disabled={saving}
                multiline
                numberOfLines={4}
                error={!!errors.questionText}
              />
            </PaperFormField>

            <PaperFormField
              label={t('explanation') || 'توضیحات'}
              error={errors.explanation}
              required
            >
              <PaperTextInput
                value={explanation}
                onChangeText={setExplanation}
                placeholder="توضیحات سوال را وارد کنید"
                disabled={saving}
                multiline
                numberOfLines={3}
                error={!!errors.explanation}
              />
            </PaperFormField>

            <PaperFormField
              label={t('difficultyLevel') || 'سطح دشواری (1-5)'}
              error={errors.difficultyLevel}
              required
            >
              <PaperTextInput
                value={difficultyLevel}
                onChangeText={setDifficultyLevel}
                placeholder="عدد بین 1 تا 5"
                disabled={saving}
                keyboardType="numeric"
                error={!!errors.difficultyLevel}
              />
            </PaperFormField>

            <PaperFormField
              label={t('chapterId') || 'شناسه فصل'}
              error={errors.chapterId}
              required
            >
              <PaperTextInput
                value={chapterId}
                onChangeText={setChapterId}
                placeholder="شناسه فصل"
                disabled={saving}
                error={!!errors.chapterId}
              />
            </PaperFormField>
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
});
