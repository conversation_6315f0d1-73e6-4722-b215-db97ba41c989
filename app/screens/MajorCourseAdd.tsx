import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { majorCourseService } from '@/services/majorCourseService';
import { router } from 'expo-router';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function MajorCourseAddScreen() {
  const [majorId, setMajorId] = useState('');
  const [courseId, setCourseId] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddMajorCourse = async () => {
    if (!majorId.trim() || !courseId.trim()) {
      Alert.alert('Validation Error', 'Major ID and Course ID cannot be empty.');
      return;
    }

    setLoading(true);
    try {
      await majorCourseService.createMajorCourse({ majorId, courseId });
      Alert.alert('Success', 'Major-Course relation added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add major-course relation. Please try again.');
      console.error('Failed to add major-course relation:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Add New Major-Course Relation</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Major ID"
        value={majorId}
        onChangeText={setMajorId}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="Course ID"
        value={courseId}
        onChangeText={setCourseId}
        editable={!loading}
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddMajorCourse}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'Adding...' : 'Add Relation'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
