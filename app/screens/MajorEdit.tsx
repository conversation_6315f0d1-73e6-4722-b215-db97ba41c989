import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView, View } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { Major, majorService } from '@/services/majorService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function MajorEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (id) {
      const fetchMajor = async () => {
        try {
          const data = await majorService.getMajor(id as string);
          setName(data.name);
          setDescription(data.description || '');
        } catch (error) {
          setAlertTitle(t('error') || 'خطا');
          setAlertMessage('خطا در دریافت جزئیات رشته برای ویرایش.');
          setShowAlert(true);
          console.error('Failed to fetch major:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchMajor();
    } else {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه رشته برای ویرایش ارائه نشده است.');
      setShowAlert(true);
      setLoading(false);
    }
  }, [id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!name.trim()) {
      newErrors.name = 'نام رشته الزامی است';
    }

    if (!description.trim()) {
      newErrors.description = 'توضیحات رشته الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateMajor = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه رشته موجود نیست.');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await majorService.updateMajor(id as string, { name, description });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('رشته با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('به‌روزرسانی رشته انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update major:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش رشته"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگذاری رشته برای ویرایش...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editMajor') || 'ویرایش رشته'}
        onSavePress={handleUpdateMajor}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('majorDetails') || 'جزئیات رشته'}>
            <PaperFormField
              label={t('majorName') || 'نام رشته'}
              error={errors.name}
              required
            >
              <PaperTextInput
                value={name}
                onChangeText={setName}
                placeholder="نام رشته را وارد کنید"
                disabled={saving}
                error={!!errors.name}
              />
            </PaperFormField>

            <PaperFormField
              label={t('majorDescription') || 'توضیحات رشته'}
              error={errors.description}
              required
            >
              <PaperTextInput
                value={description}
                onChangeText={setDescription}
                placeholder="توضیحات رشته را وارد کنید"
                disabled={saving}
                multiline
                numberOfLines={3}
                error={!!errors.description}
              />
            </PaperFormField>
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
});
