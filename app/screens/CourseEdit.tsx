import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView, View, ScrollView } from "react-native";
import { Surface, ActivityIndicator } from 'react-native-paper';
import { Course, courseService } from "@/services/courseService";
import { courseGroupService, CourseGroup } from "@/services/courseGroupService";
import KeyboardAvoidingWrapper from "@/components/KeyboardAvoidingWrapper";
import { useLocalSearchParams, router } from "expo-router";
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
  PaperTitle,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function CourseEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [coefficient, setCoefficient] = useState("");
  const [negativeMarkingFactor, setNegativeMarkingFactor] = useState("");
  const [courseGroupId, setCourseGroupId] = useState<string | null>(null);
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    const fetchCourseData = async () => {
      if (!id) {
        setAlertTitle(t('error') || 'خطا');
        setAlertMessage("شناسه درس برای ویرایش ارائه نشده است.");
        setShowAlert(true);
        setLoading(false);
        return;
      }

      try {
        const [courseData, groupsData] = await Promise.all([
          courseService.getCourse(id as string),
          courseGroupService.getCourseGroups(),
        ]);

        setName(courseData.name);
        setDescription(courseData.description || '');
        setCoefficient(courseData.coefficient.toString());
        setNegativeMarkingFactor(courseData.negative_marking_factor.toString());
        setCourseGroups(groupsData);
        setCourseGroupId(courseData.course_group_id.toString());
      } catch (error) {
        setAlertTitle(t('error') || 'خطا');
        setAlertMessage("خطا در دریافت جزئیات درس برای ویرایش.");
        setShowAlert(true);
        console.error('Failed to fetch course data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchCourseData();
  }, [id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!name.trim()) {
      newErrors.name = 'نام درس الزامی است';
    }

    if (!description.trim()) {
      newErrors.description = 'توضیحات درس الزامی است';
    }

    if (!coefficient.trim()) {
      newErrors.coefficient = 'ضریب الزامی است';
    } else if (isNaN(Number(coefficient)) || Number(coefficient) <= 0) {
      newErrors.coefficient = 'ضریب باید عدد مثبت باشد';
    }

    if (!negativeMarkingFactor.trim()) {
      newErrors.negativeMarkingFactor = 'ضریب نمره منفی الزامی است';
    } else if (isNaN(Number(negativeMarkingFactor)) || Number(negativeMarkingFactor) < 0) {
      newErrors.negativeMarkingFactor = 'ضریب نمره منفی باید عدد غیرمنفی باشد';
    }

    if (courseGroupId === null) {
      newErrors.courseGroupId = 'انتخاب گروه درسی الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateCourse = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage("شناسه درس موجود نیست.");
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await courseService.updateCourse(id as string, {
        name,
        description,
        coefficient: parseFloat(coefficient),
        negative_marking_factor: parseFloat(negativeMarkingFactor),
        course_group_id: parseInt(courseGroupId!, 10),
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage("درس با موفقیت به‌روزرسانی شد!");
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage("به‌روزرسانی درس انجام نشد. لطفا دوباره امتحان کنید.");
      setShowAlert(true);
      console.error("Failed to update course:", error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش درس"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگیری جزئیات درس...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>ویرایش درس</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="نام درس"
        value={name}
        onChangeText={setName}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات درس"
        value={description}
        onChangeText={setDescription}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="ضریب"
        value={coefficient}
        onChangeText={setCoefficient}
        keyboardType="numeric"
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="ضریب نمره منفی"
        value={negativeMarkingFactor}
        onChangeText={setNegativeMarkingFactor}
        keyboardType="numeric"
        editable={!saving}
      />
      <ThemedText style={styles.pickerLabel}>گروه درسی:</ThemedText>
      <Picker
        selectedValue={courseGroupId}
        onValueChange={(itemValue: string) => setCourseGroupId(itemValue)}
        style={styles.picker}
        enabled={!saving}
      >
        {courseGroups.map((group) => (
          <Picker.Item key={group.id} label={group.name} value={group.id} />
        ))}
      </Picker>
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateCourse}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? "در حال ذخیره..." : "ذخیره تغییرات"}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: "center",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    height: 40,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: "#fff",
  },
  pickerLabel: {
    fontSize: 16,
    marginBottom: 5,
    color: "#333",
  },
  picker: {
    height: 50,
    width: "100%",
    marginBottom: 15,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  button: {
    backgroundColor: "#007bff",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: "#a0c9ff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
});
