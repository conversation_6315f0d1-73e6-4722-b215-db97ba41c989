import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { sessionService } from '@/services/sessionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function SessionAddScreen() {
  const [userId, setUserId] = useState('');
  const [examId, setExamId] = useState('');
  const [score, setScore] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!userId.trim()) {
      newErrors.userId = 'شناسه کاربر الزامی است';
    }

    if (!examId.trim()) {
      newErrors.examId = 'شناسه آزمون الزامی است';
    }

    if (!score.trim()) {
      newErrors.score = 'نمره الزامی است';
    } else if (isNaN(Number(score)) || Number(score) < 0 || Number(score) > 100) {
      newErrors.score = 'نمره باید عددی بین 0 تا 100 باشد';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddSession = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await sessionService.createSession({ userId, examId, score: parseInt(score) });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('جلسه با موفقیت اضافه شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('افزودن جلسه انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add session:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addSession') || 'افزودن جلسه'}
        onSavePress={() => {}} // Handled by form submit button
        saveDisabled={loading}
        showSave={false} // Using form submit button instead
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('sessionDetails') || 'جزئیات جلسه'}>
            <PaperFormField
              label={t('userId') || 'شناسه کاربر'}
              error={errors.userId}
              required
            >
              <PaperTextInput
                value={userId}
                onChangeText={setUserId}
                placeholder="شناسه کاربر را وارد کنید"
                disabled={loading}
                error={!!errors.userId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('examId') || 'شناسه آزمون'}
              error={errors.examId}
              required
            >
              <PaperTextInput
                value={examId}
                onChangeText={setExamId}
                placeholder="شناسه آزمون را وارد کنید"
                disabled={loading}
                error={!!errors.examId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('score') || 'نمره (0-100)'}
              error={errors.score}
              required
            >
              <PaperTextInput
                value={score}
                onChangeText={setScore}
                placeholder="نمره را وارد کنید"
                disabled={loading}
                keyboardType="numeric"
                error={!!errors.score}
              />
            </PaperFormField>

            <PaperButton
              title={loading ? 'در حال افزودن...' : t('addSession') || 'افزودن جلسه'}
              onPress={handleAddSession}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  submitButton: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
