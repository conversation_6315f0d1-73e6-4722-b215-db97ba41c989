import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView, View } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { Session, sessionService } from '@/services/sessionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function SessionEditScreen() {
  const { id } = useLocalSearchParams();
  const [userId, setUserId] = useState('');
  const [examId, setExamId] = useState('');
  const [score, setScore] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (id) {
      const fetchSession = async () => {
        try {
          const data = await sessionService.getSession(id as string);
          setUserId(data.userId);
          setExamId(data.examId);
          setScore(data.score.toString());
        } catch (error) {
          setAlertTitle(t('error') || 'خطا');
          setAlertMessage('خطا در دریافت جزئیات جلسه برای ویرایش.');
          setShowAlert(true);
          console.error('Failed to fetch session:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchSession();
    } else {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه جلسه برای ویرایش ارائه نشده است.');
      setShowAlert(true);
      setLoading(false);
    }
  }, [id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!userId.trim()) {
      newErrors.userId = 'شناسه کاربر الزامی است';
    }

    if (!examId.trim()) {
      newErrors.examId = 'شناسه آزمون الزامی است';
    }

    if (!score.trim()) {
      newErrors.score = 'نمره الزامی است';
    } else if (isNaN(Number(score)) || Number(score) < 0 || Number(score) > 100) {
      newErrors.score = 'نمره باید عددی بین 0 تا 100 باشد';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateSession = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه جلسه موجود نیست.');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await sessionService.updateSession(id as string, { userId, examId, score: parseInt(score) });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('جلسه با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('به‌روزرسانی جلسه انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update session:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش جلسه"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگذاری جلسه برای ویرایش...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editSession') || 'ویرایش جلسه'}
        onSavePress={handleUpdateSession}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('sessionDetails') || 'جزئیات جلسه'}>
            <PaperFormField
              label={t('userId') || 'شناسه کاربر'}
              error={errors.userId}
              required
            >
              <PaperTextInput
                value={userId}
                onChangeText={setUserId}
                placeholder="شناسه کاربر را وارد کنید"
                disabled={saving}
                error={!!errors.userId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('examId') || 'شناسه آزمون'}
              error={errors.examId}
              required
            >
              <PaperTextInput
                value={examId}
                onChangeText={setExamId}
                placeholder="شناسه آزمون را وارد کنید"
                disabled={saving}
                error={!!errors.examId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('score') || 'نمره (0-100)'}
              error={errors.score}
              required
            >
              <PaperTextInput
                value={score}
                onChangeText={setScore}
                placeholder="نمره را وارد کنید"
                disabled={saving}
                keyboardType="numeric"
                error={!!errors.score}
              />
            </PaperFormField>
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
});
