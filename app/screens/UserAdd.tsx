import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { userService } from '@/services/userService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperRadioGroup,
  PaperAlertDialog,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function UserAddScreen() {
  const [email, setEmail] = useState('');
  const [mobile, setMobile] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState('');
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!email.trim()) {
      newErrors.email = t('emailRequired') || 'ایمیل الزامی است';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = t('emailInvalid') || 'ایمیل نامعتبر است';
    }

    if (!mobile.trim()) {
      newErrors.mobile = t('mobileRequired') || 'شماره موبایل الزامی است';
    } else if (!/^09\d{9}$/.test(mobile)) {
      newErrors.mobile = t('mobileInvalid') || 'شماره موبایل نامعتبر است';
    }

    if (!password.trim()) {
      newErrors.password = t('passwordRequired') || 'رمز عبور الزامی است';
    } else if (password.length < 6) {
      newErrors.password = t('passwordTooShort') || 'رمز عبور باید حداقل 6 کاراکتر باشد';
    }

    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = t('confirmPasswordRequired') || 'تأیید رمز عبور الزامی است';
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = t('passwordsDoNotMatch') || 'رمز عبور و تأیید آن یکسان نیستند';
    }

    if (!firstName.trim()) {
      newErrors.firstName = t('firstNameRequired') || 'نام الزامی است';
    }

    if (!lastName.trim()) {
      newErrors.lastName = t('lastNameRequired') || 'نام خانوادگی الزامی است';
    }

    if (!role.trim()) {
      newErrors.role = t('roleRequired') || 'نقش الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddUser = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await userService.createUser({
        email,
        mobile,
        password_hash: password,
        first_name: firstName,
        last_name: lastName,
        role,
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage(t('userAddSuccess') || 'کاربر با موفقیت اضافه شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage(t('userAddError') || 'افزودن کاربر انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add user:', error);
    } finally {
      setLoading(false);
    }
  };

  const roleOptions = [
    { label: t('admin') || 'مدیر', value: 'admin' },
    { label: t('teacher') || 'مدرس', value: 'teacher' },
    { label: t('student') || 'دانشجو', value: 'student' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addUser') || 'افزودن کاربر'}
        onSavePress={() => {}} // Handled by form submit button
        saveDisabled={loading}
        showSave={false} // Using form submit button instead
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('accountInfo') || 'اطلاعات حساب کاربری'}>
            <PaperFormField
              label={t('email') || 'ایمیل'}
              error={errors.email}
              required
            >
              <PaperTextInput
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                disabled={loading}
                keyboardType="email-address"
                autoCapitalize="none"
                error={!!errors.email}
              />
            </PaperFormField>

            <PaperFormField
              label={t('mobile') || 'شماره موبایل'}
              error={errors.mobile}
              required
            >
              <PaperTextInput
                value={mobile}
                onChangeText={setMobile}
                placeholder="***********"
                disabled={loading}
                keyboardType="phone-pad"
                error={!!errors.mobile}
              />
            </PaperFormField>

            <PaperFormField
              label={t('password') || 'رمز عبور'}
              error={errors.password}
              required
            >
              <PaperTextInput
                value={password}
                onChangeText={setPassword}
                placeholder="رمز عبور"
                disabled={loading}
                secureTextEntry
                error={!!errors.password}
              />
            </PaperFormField>

            <PaperFormField
              label={t('confirmPassword') || 'تأیید رمز عبور'}
              error={errors.confirmPassword}
              required
            >
              <PaperTextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="تأیید رمز عبور"
                disabled={loading}
                secureTextEntry
                error={!!errors.confirmPassword}
              />
            </PaperFormField>
          </PaperFormSection>

          <PaperFormSection title={t('personalInfo') || 'اطلاعات شخصی'}>
            <PaperFormField
              label={t('firstName') || 'نام'}
              error={errors.firstName}
              required
            >
              <PaperTextInput
                value={firstName}
                onChangeText={setFirstName}
                placeholder="نام"
                disabled={loading}
                error={!!errors.firstName}
              />
            </PaperFormField>

            <PaperFormField
              label={t('lastName') || 'نام خانوادگی'}
              error={errors.lastName}
              required
            >
              <PaperTextInput
                value={lastName}
                onChangeText={setLastName}
                placeholder="نام خانوادگی"
                disabled={loading}
                error={!!errors.lastName}
              />
            </PaperFormField>

            <PaperFormField
              label={t('role') || 'نقش'}
              error={errors.role}
              required
            >
              <PaperRadioGroup
                options={roleOptions}
                value={role}
                onValueChange={setRole}
                disabled={loading}
              />
            </PaperFormField>
          </PaperFormSection>

          <PaperButton
            title={loading ? 'در حال افزودن...' : t('addUser') || 'افزودن کاربر'}
            onPress={handleAddUser}
            disabled={loading}
            loading={loading}
            mode="contained"
            style={styles.submitButton}
            icon="account-plus"
          />
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  submitButton: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
