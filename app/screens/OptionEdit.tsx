import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView, View } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { Option, optionService } from '@/services/optionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperSwitch,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function OptionEditScreen() {
  const { id } = useLocalSearchParams();
  const [text, setText] = useState('');
  const [questionId, setQuestionId] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (id) {
      const fetchOption = async () => {
        try {
          const data = await optionService.getOption(id as string);
          setText(data.text);
          setQuestionId(data.questionId);
          setIsCorrect(data.isCorrect);
        } catch (error) {
          setAlertTitle(t('error') || 'خطا');
          setAlertMessage('خطا در دریافت جزئیات گزینه برای ویرایش.');
          setShowAlert(true);
          console.error('Failed to fetch option:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchOption();
    } else {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه گزینه برای ویرایش ارائه نشده است.');
      setShowAlert(true);
      setLoading(false);
    }
  }, [id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!text.trim()) {
      newErrors.text = 'متن گزینه الزامی است';
    }

    if (!questionId.trim()) {
      newErrors.questionId = 'شناسه سوال الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateOption = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه گزینه موجود نیست.');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await optionService.updateOption(id as string, { text, questionId, isCorrect });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('گزینه با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('به‌روزرسانی گزینه انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update option:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش گزینه"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگذاری گزینه برای ویرایش...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editOption') || 'ویرایش گزینه'}
        onSavePress={handleUpdateOption}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('optionDetails') || 'جزئیات گزینه'}>
            <PaperFormField
              label={t('optionText') || 'متن گزینه'}
              error={errors.text}
              required
            >
              <PaperTextInput
                value={text}
                onChangeText={setText}
                placeholder="متن گزینه را وارد کنید"
                disabled={saving}
                multiline
                numberOfLines={3}
                error={!!errors.text}
              />
            </PaperFormField>

            <PaperFormField
              label={t('questionId') || 'شناسه سوال'}
              error={errors.questionId}
              required
            >
              <PaperTextInput
                value={questionId}
                onChangeText={setQuestionId}
                placeholder="شناسه سوال"
                disabled={saving}
                error={!!errors.questionId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('isCorrectAnswer') || 'پاسخ صحیح'}
            >
              <View style={styles.switchContainer}>
                <PaperBody style={styles.switchLabel}>
                  {isCorrect ? 'این گزینه پاسخ صحیح است' : 'این گزینه پاسخ صحیح نیست'}
                </PaperBody>
                <PaperSwitch
                  value={isCorrect}
                  onValueChange={setIsCorrect}
                  disabled={saving}
                />
              </View>
            </PaperFormField>
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  switchLabel: {
    flex: 1,
    marginRight: 16,
  },
});
