import React, { useState, useEffect } from 'react';
import { StyleSheet, SafeAreaView, ScrollView, View } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { examService, Exam } from '@/services/examService';
import { majorService, Major } from '@/services/majorService';
import { questionService, Question } from '@/services/questionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperTitle,
  PaperBody,
  PaperCard,
  PaperCheckbox,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function ExamEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState('');
  const [durationMinutes, setDurationMinutes] = useState('');
  const [instructions, setInstructions] = useState('');
  const [majorId, setMajorId] = useState<number | undefined>(undefined);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [majors, setMajors] = useState<Major[]>([]);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    const loadExamData = async () => {
      if (!id || typeof id !== 'string') {
        setAlertTitle(t('error') || 'خطا');
        setAlertMessage('شناسه آزمون نامعتبر است');
        setShowAlert(true);
        return;
      }

      try {
        setLoading(true);
        const [examData, majorsData, questionsData] = await Promise.all([
          examService.getExam(id),
          majorService.getMajors(),
          questionService.getQuestions(),
        ]);

        setName(examData.name);
        setDurationMinutes(examData.duration_minutes.toString());
        setInstructions(examData.instructions);
        setMajorId(examData.majorId);
        setSelectedQuestions(examData.questionIds || []);
        setMajors(majorsData);
        setQuestions(questionsData);
      } catch (error) {
        setAlertTitle(t('error') || 'خطا');
        setAlertMessage('خطا در بارگیری اطلاعات آزمون');
        setShowAlert(true);
        console.error('Failed to load exam data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadExamData();
  }, [id]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!name.trim()) {
      newErrors.name = 'نام آزمون الزامی است';
    }

    if (!durationMinutes.trim()) {
      newErrors.durationMinutes = 'مدت آزمون الزامی است';
    } else if (isNaN(Number(durationMinutes)) || Number(durationMinutes) <= 0) {
      newErrors.durationMinutes = 'مدت آزمون باید عدد مثبت باشد';
    }

    if (!instructions.trim()) {
      newErrors.instructions = 'دستورالعمل الزامی است';
    }

    if (majorId === undefined) {
      newErrors.majorId = 'انتخاب رشته الزامی است';
    }

    if (selectedQuestions.length === 0) {
      newErrors.questions = 'حداقل یک سوال باید انتخاب شود';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateExam = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id || typeof id !== 'string') {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه آزمون نامعتبر است');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await examService.updateExam(id, {
        name,
        duration_minutes: Number(durationMinutes),
        instructions,
        majorId: Number(majorId),
        questionIds: selectedQuestions,
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('آزمون با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('به‌روزرسانی آزمون انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update exam:', error);
    } finally {
      setSaving(false);
    }
  };

  const toggleQuestionSelection = (questionId: string) => {
    setSelectedQuestions((prevSelected) =>
      prevSelected.includes(questionId)
        ? prevSelected.filter((id) => id !== questionId)
        : [...prevSelected, questionId]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش آزمون"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگیری...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editExam') || 'ویرایش آزمون'}
        onSavePress={handleUpdateExam}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <ScrollView>
          <KeyboardAvoidingWrapper>
            <PaperFormSection title="جزئیات آزمون">
              <PaperFormField
                label="نام آزمون"
                error={errors.name}
                required
              >
                <PaperTextInput
                  value={name}
                  onChangeText={setName}
                  placeholder="نام آزمون را وارد کنید"
                  disabled={saving}
                  error={!!errors.name}
                />
              </PaperFormField>

              <PaperFormField
                label="مدت آزمون (دقیقه)"
                error={errors.durationMinutes}
                required
              >
                <PaperTextInput
                  value={durationMinutes}
                  onChangeText={setDurationMinutes}
                  placeholder="مدت آزمون به دقیقه"
                  disabled={saving}
                  keyboardType="numeric"
                  error={!!errors.durationMinutes}
                />
              </PaperFormField>

              <PaperFormField
                label="دستورالعمل"
                error={errors.instructions}
                required
              >
                <PaperTextInput
                  value={instructions}
                  onChangeText={setInstructions}
                  placeholder="دستورالعمل آزمون را وارد کنید"
                  disabled={saving}
                  multiline
                  numberOfLines={3}
                  error={!!errors.instructions}
                />
              </PaperFormField>

              <PaperFormField
                label="رشته تحصیلی"
                error={errors.majorId}
                required
              >
                <View style={styles.majorSelector}>
                  <PaperTitle size="small" style={styles.majorSelectorTitle}>
                    {majorId ? majors.find(m => Number(m.id) === majorId)?.name : 'انتخاب رشته تحصیلی'}
                  </PaperTitle>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.majorOptions}>
                    {majors.map((major) => (
                      <PaperButton
                        key={major.id}
                        title={major.name}
                        mode={majorId === Number(major.id) ? "contained" : "outlined"}
                        onPress={() => setMajorId(Number(major.id))}
                        disabled={saving}
                        style={styles.majorOption}
                        compact
                      />
                    ))}
                  </ScrollView>
                </View>
              </PaperFormField>
            </PaperFormSection>

            <PaperFormSection title="انتخاب سوال‌ها">
              <PaperFormField
                label=""
                error={errors.questions}
              >
                <View style={styles.questionsContainer}>
                  {questions.map((question) => (
                    <PaperCard key={question.id} style={styles.questionCard}>
                      <View style={styles.questionRow}>
                        <PaperCheckbox
                          status={selectedQuestions.includes(question.id) ? 'checked' : 'unchecked'}
                          onPress={() => toggleQuestionSelection(question.id)}
                          disabled={saving}
                        />
                        <PaperBody style={styles.questionText}>
                          {question.question_text}
                        </PaperBody>
                      </View>
                    </PaperCard>
                  ))}
                </View>
              </PaperFormField>
            </PaperFormSection>
          </KeyboardAvoidingWrapper>
        </ScrollView>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

// --- Stylesheet ---

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6B7280",
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6", // gray-100
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1F2937", // gray-800
    flex: 1,
    ...rtlStyle.marginLeft(8),
    textAlign: rtlStyle.textAlign.start,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#FEF2F2", // red-50
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
  },
  characterCount: {
    fontSize: 12,
    color: "#6B7280", // gray-500
  },
  input: {
    borderWidth: 1,
    borderColor: "#D1D5DB",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: "#1F2937",
    backgroundColor: "#FFFFFF",
    textAlign: rtlStyle.textAlign.start,
  },
  inputError: {
    borderColor: "#EF4444", // red-500
    backgroundColor: "#FEF2F2", // red-50
  },
  errorText: {
    fontSize: 14,
    color: "#EF4444", // red-500
    marginTop: 4,
    textAlign: rtlStyle.textAlign.start,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  submitButton: {
    backgroundColor: "#2563EB",
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },
  submitButtonDisabled: {
    backgroundColor: "#9CA3AF",
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ExamEditScreen;
