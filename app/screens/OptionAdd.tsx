import React, { useState } from 'react';
import { StyleSheet, SafeAreaView, View } from 'react-native';
import { Surface } from 'react-native-paper';
import { optionService } from '@/services/optionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router, useLocalSearchParams } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperSwitch,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function OptionAddScreen() {
  const { questionId } = useLocalSearchParams();
  const [text, setText] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!text.trim()) {
      newErrors.text = 'متن گزینه الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddOption = async () => {
    if (!validateForm()) {
      return;
    }

    if (!questionId) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه سوال موجود نیست. لطفاً از یک سوال مراجعه کنید.');
      setShowAlert(true);
      return;
    }

    setLoading(true);
    try {
      await optionService.createOption({ text, questionId: questionId as string, isCorrect });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('گزینه با موفقیت اضافه شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('افزودن گزینه انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add option:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addOption') || 'افزودن گزینه'}
        onSavePress={() => {}} // Handled by form submit button
        saveDisabled={loading}
        showSave={false} // Using form submit button instead
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('optionDetails') || 'جزئیات گزینه'}>
            {questionId && (
              <PaperBody style={styles.questionInfo}>
                افزودن گزینه برای سوال شناسه: {questionId}
              </PaperBody>
            )}

            <PaperFormField
              label={t('optionText') || 'متن گزینه'}
              error={errors.text}
              required
            >
              <PaperTextInput
                value={text}
                onChangeText={setText}
                placeholder="متن گزینه را وارد کنید"
                disabled={loading}
                multiline
                numberOfLines={3}
                error={!!errors.text}
              />
            </PaperFormField>

            <PaperFormField
              label={t('isCorrectAnswer') || 'پاسخ صحیح'}
            >
              <View style={styles.switchContainer}>
                <PaperBody style={styles.switchLabel}>
                  {isCorrect ? 'این گزینه پاسخ صحیح است' : 'این گزینه پاسخ صحیح نیست'}
                </PaperBody>
                <PaperSwitch
                  value={isCorrect}
                  onValueChange={setIsCorrect}
                  disabled={loading}
                />
              </View>
            </PaperFormField>

            <PaperButton
              title={loading ? 'در حال افزودن...' : t('addOption') || 'افزودن گزینه'}
              onPress={handleAddOption}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  questionInfo: {
    marginBottom: 16,
    textAlign: 'center',
    color: '#666',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  switchLabel: {
    flex: 1,
    marginRight: 16,
  },
  submitButton: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
